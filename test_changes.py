#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试脚本：验证压缩对话框中"2048论坛"样式修改
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
    from PyQt5.QtCore import Qt
    
    # 导入主程序
    from simple_folder_viewer import CompressDialog, initialize_qt
    
    class TestWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("测试压缩对话框样式")
            self.setGeometry(100, 100, 400, 200)
            
            # 创建中央部件
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # 创建布局
            layout = QVBoxLayout(central_widget)
            
            # 创建测试按钮
            test_button = QPushButton("打开压缩对话框")
            test_button.clicked.connect(self.show_compress_dialog)
            layout.addWidget(test_button)
            
            self.compress_dialog = None
        
        def show_compress_dialog(self):
            """显示压缩对话框"""
            if self.compress_dialog:
                self.compress_dialog.close()
            
            self.compress_dialog = CompressDialog(self)
            self.compress_dialog.show()
    
    def main():
        """主函数"""
        try:
            # 初始化Qt应用
            app = initialize_qt()
            
            # 创建测试窗口
            window = TestWindow()
            window.show()
            
            print("测试窗口已启动")
            print("点击按钮查看修改后的压缩对话框样式")
            print("检查项目：")
            print("1. '2048论坛' 文字是否恢复为原来的深色样式")
            print("2. 密码输入框是否恢复为原来的边框样式")
            print("3. 密码显示区域是否恢复为原来的样式")
            print("4. '2048论坛' 标签的上下边距是否增加了（从4px增加到6px）")
            
            # 运行应用
            sys.exit(app.exec_())
            
        except Exception as e:
            print(f"测试过程中出错: {str(e)}")
            import traceback
            traceback.print_exc()
    
    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print(f"导入模块失败: {str(e)}")
    print("请确保PyQt5已正确安装")
    sys.exit(1)
